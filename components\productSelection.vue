<template>
  <view>
    <Search :searchType="0" @search-result="getSearchResult" v-if="isShowSearch" />
    <goodsList :selectGoods="true" :type="type" :searchResults="searchResults" :relatedOrderGoods="relatedOrderGoods"
      :deselectItemList="deselectItemList" :deselectItem="deselectItem" :warehouseData="warehouseData"
      @selectedGoodsList="selectedGoodsList" @selectType="selectType" :key="'goodsList-' + type" />

    <!-- 遮罩层 -->
    <view v-if="shoppingCartShow" class="cart_mask" @click="expandShoppingCart"></view>

    <!-- 购物车面板 -->
    <view v-if="shoppingCartShow" class="cart_popup" :animation="cartAnimation">
      <view class="cart_container">
        <view class="categoryConfirmationBtn">
          <view>已选商品</view>
          <view class="blueFont" @click="clearSelectGoodsList">清空</view>
        </view>
        <view class="divider"></view>
        <view class="goods">
          <view class="goods_item" v-for="(item, index) in selectGoodsList" :key="index">
            <view class="goods_left">
              <image class="goods_img" :src="item.imgurl || '/static/img/logo.png'" mode=""></image>
              <view class="goods_info">
                <view class="goods_extra">
                  {{ item.name }}
                  <view class="stock-tag" v-if="type == 6">
                    库存：{{ Math.floor(item.total_stock || 0) }} - {{ Math.floor(item.remaining_stock || 0) }}
                  </view>
                </view>
                <view class="goods_code">{{ item.code }}</view>
                <view class="goods_price">
                  <text class="price">￥{{ item.purchase_price || "未填写" }}</text>
                  <text class="unit">/{{ item.unit_name || "未填写" }}</text>
                </view>
              </view>
            </view>
            <view class="goods_num">
              <view class="goods_num_reduce" @click.stop="reduceNum(item)">
                <i-reduce-one theme="outline" size="20" fill="#3894ff" />
              </view>
              <view class="goods_num_input">{{ item.quantity || 0 }}</view>
              <view class="goods_num_add" @click.stop="addNum(item)">
                <i-add-one theme="filled" size="20" fill="#3894ff" />
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 操作按钮（展开时） -->
      <view class="operatingButton popup-btn">
        <view class="selectGoods">
          <u-button type="warning" @click="expandShoppingCart">
            <i-shopping theme="outline" size="24" fill="#fff" />
            已选择({{ selectGoodsList.length || 0 }})
          </u-button>
        </view>
        <view class="selectGoods">
          <u-button type="primary" @click="confirmSelectGoods">
            <i-check-small theme="outline" size="24" fill="#fff" />选好了
          </u-button>
        </view>
      </view>
      <Input style="height: 100%" />
    </view>

    <!-- 底部操作按钮（收缩时） -->
    <view v-if="!shoppingCartShow" class="operatingButton fixed-btn">
      <view class="selectGoods">
        <u-button type="warning" @click="expandShoppingCart">
          <i-shopping theme="outline" size="24" fill="#fff" />
          已选择({{ selectGoodsList.length || 0 }})
        </u-button>
      </view>
      <view class="selectGoods">
        <u-button type="primary" @click="confirmSelectGoods">
          <i-check-small theme="outline" size="24" fill="#fff" />选好了
        </u-button>
      </view>
    </view>

    <Input style="height: 100%" v-if="!shoppingCartShow" />

    <ProductDetails :selectGoodsList="selectGoodsList" :selectedGoodsQuantity="selectedGoodsQuantity" />
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue';
import Input from "@/components/input/input.vue";
import goodsList from "@/components/goodsList.vue";
import productDetails from "@/components/productDetails.vue";
import Search from "@/components/search.vue";
import eventBus from "@/utils/eventBus";

// 接口定义
interface GoodsItem {
  id: string;
  item?: string;
  code: string;
  name: string;
  item_name?: string;
  imgurl?: string;
  purchase_price?: number;
  unit_name?: string;
  quantity?: number;
  total_stock?: number;
  remaining_stock?: number;
  conversion_rate?: number;
  sales_order_item?: boolean;
  from_sales_order?: boolean;
  related_order_id?: string;
  warehouse?: string;
  warehouse_name?: string;
  // goodsList 组件所需的必需属性
  is_active: boolean;
  isWarehouse: boolean;
  selected: boolean;
}

// 响应式状态
const relatedOrderGoods = ref<GoodsItem[]>([]); //关联订单商品
const selectGoodsList = ref<GoodsItem[]>([]); //选择的商品列表
const searchResults = ref<GoodsItem[]>([]); //搜索内容
const deselectItem = ref<GoodsItem | undefined>(undefined); //取消选择商品
const deselectItemList = ref<string[]>([]); //取消选择商品列表
const shoppingCartShow = ref<boolean>(false);
const cartAnimation = ref<any>(null);
const isShowWarehouse = ref<string | null>(null); //在商品列表是否显示仓库字段
const type = ref<number>(0);
const isShowSearch = ref<boolean>(false); //是否选择商品
const related_order_id = ref<string>(''); // 关联订单ID
const source = ref<string>(''); // 商品选择来源

const warehouseData = reactive({
  warehouse: '', //仓库id
  warehouse_name: '', //仓库名称
});

// 计算已选商品数量
const selectedGoodsQuantity = ref<number>(0);
// 方法定义
const getSearchResult = (newVal: GoodsItem[]) => {
  searchResults.value = newVal;
};

const selectedGoodsList = (data: GoodsItem[]) => {
  console.log('接收到商品数据:', data);
  if (!data || !Array.isArray(data)) {
    console.warn('接收到的数据无效或不是数组');
    return;
  }
  selectGoodsList.value = data;
};

const selectType = (newType: number) => {
  type.value = newType;
};

const expandShoppingCart = () => {
  if (selectGoodsList.value.length <= 0 && !shoppingCartShow.value) {
    uni.showToast({
      title: "您需要先挑选商品",
      icon: "none",
    });
    return;
  }
  if (!shoppingCartShow.value) {
    // 展开动画
    shoppingCartShow.value = true;
    nextTick(() => {
      let animation = uni.createAnimation({
        duration: 300,
        timingFunction: "ease",
      });
      animation.translateY(0).opacity(1).step();
      cartAnimation.value = animation.export();
    });
  } else {
    // 收缩动画
    let animation = uni.createAnimation({
      duration: 300,
      timingFunction: "ease",
    });
    animation.translateY("100%").opacity(0).step();
    cartAnimation.value = animation.export();
    setTimeout(() => {
      shoppingCartShow.value = false;
    }, 300);
  }
};

// 重新计算相同item的remaining_stock
const recalculateRemainingStock = (itemId: string) => {
  // 计算相同item的总用量
  let totalUsedStock = 0;
  selectGoodsList.value.forEach(goods => {
    if (goods.item === itemId) {
      totalUsedStock += Number(goods.quantity || 0) * Number(goods.conversion_rate || 1);
    }
  });

  // 更新所有相同item的remaining_stock
  selectGoodsList.value.forEach(goods => {
    if (goods.item === itemId) {
      goods.remaining_stock = totalUsedStock;
    }
  });
};

const reduceNum = (item: GoodsItem) => {
  if (item.quantity) {
    item.quantity -= 1;
  }

  if (type.value == 6) {//如果是零售单，减少数量，需要变换库存减少量
    const total = 1 * Number(item.conversion_rate);
    const reduceStock = (item.remaining_stock || 0) - total;

    // 循环selectGoodsList，对所有相同item的商品都减少库存
    selectGoodsList.value.forEach((goods) => {
      if (goods.item === item.item) {
        goods.remaining_stock = reduceStock;
      }
    });
  }

  if ((item.quantity || 0) <= 0) {
    deselectItem.value = item;
    const index = selectGoodsList.value.indexOf(item);
    if (index > -1) {
      selectGoodsList.value.splice(index, 1);
    }

    // 当商品被移除时，重新计算相同item的remaining_stock
    if (type.value == 6 && item.item) {
      recalculateRemainingStock(item.item);
    }

    if (type.value == 4) {
      uni.removeStorageSync('returnWarehouse');
      eventBus.$emit('returnWarehouse', {});
    }
  }
};

const addNum = (item: GoodsItem) => {
  item.quantity = Number(item.quantity || 0) + 1;

  if (type.value == 6) {//如果是零售单，增加数量，需要变换库存减少量
    const oldRemainingStock = item.remaining_stock || 0;
    const total = 1 * Number(item.conversion_rate);
    const AddStock = (item.remaining_stock || 0) + total;

    if (AddStock >= (item.total_stock || 0)) {
      uni.showToast({
        title: "当前库存不足",
        icon: "none",
        mask: true,
      });
      item.quantity -= 1;
      selectGoodsList.value.forEach((goods) => {
        if (goods.item === item.item) {
          goods.remaining_stock = oldRemainingStock;
        }
      });
      return;
    }

    // 更新所有相同item的remaining_stock
    selectGoodsList.value.forEach((goods) => {
      if (goods.item === item.item) {
        goods.remaining_stock = AddStock;
      }
    });
  }
};

const confirmSelectGoods = () => {
  console.log("确认选择的商品列表:", selectGoodsList.value);
  eventBus.$emit("selectGoodsList", selectGoodsList.value);

  if (type.value === 0 || type.value === 4 || type.value == 7) {
    uni.navigateBack({
      delta: 2,
    });
  } else {
    uni.navigateBack({
      delta: 1,
    });
  }
};

const clearSelectGoodsList = () => {
  if (type.value == 4) {
    uni.removeStorageSync('returnWarehouse');
    eventBus.$emit('returnWarehouse', {});
  }
  selectGoodsList.value.forEach((item) => {
    if (item.item) {
      deselectItemList.value.push(item.item);
    }
  });
  selectGoodsList.value = [];
};

// 处理页面加载参数的函数
const handlePageLoad = (options: any) => {
  console.log("productSelection onLoad options:", options);

  if (options.type) {
    type.value = Number(options.type);
    if (type.value == 0 || type.value == 4 || type.value == 7) {
      isShowSearch.value = false;
    } else {
      isShowSearch.value = true;
    }
  }

  if (options.isShowWarehouse) {
    isShowWarehouse.value = options.isShowWarehouse;
  }

  // 保存关联订单ID
  if (options.related_order_id) {
    related_order_id.value = options.related_order_id;
    console.log('保存关联订单ID:', related_order_id.value);
  }

  // 保存来源标记，用于区分是从普通添加还是从关联物品按钮进入
  if (options.source) {
    source.value = options.source;
    console.log('商品选择来源:', source.value);
  }

  if (options.warehouse && options.warehouse_name) {
    warehouseData.warehouse = String(options.warehouse);
    warehouseData.warehouse_name = options.warehouse_name;
    console.log('设置仓库信息:', warehouseData);
  } else if (options.warehouse) {
    // 只有warehouse没有warehouse_name的情况
    warehouseData.warehouse = String(options.warehouse);
    console.log('只设置仓库ID:', warehouseData);
  }
};

// 生命周期钩子
onMounted(() => {
  const pages = uni.getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options || {};
  if (options) {
    handlePageLoad(options);
  }
});

// 重新计算相同item的remaining_stock





</script>

<style lang="scss" scoped>
::v-deep .cart-popup-slide-enter-active,
::v-deep .cart-popup-slide-leave-active {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s;
}

::v-deep .cart-popup-slide-enter-from,
::v-deep .cart-popup-slide-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

::v-deep .cart-popup-slide-enter-to,
::v-deep .cart-popup-slide-leave-from {
  transform: translateY(0);
  opacity: 1;
}

.cartAndBtn {
  height: 900rpx;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  background-color: #fff;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selectGoods {
  width: 50%;
}

.cart_container {
  height: 800rpx;
  background-color: #fff;
}

.categoryConfirmationBtn {
  width: 90%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
  padding: 20rpx 0;
}

.divider {
  border: 1px solid #ccc;
  width: 100%;
}

.goods_item {
  width: 90%;
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin: 0 auto;
  border-bottom: 1px solid #f0f0f0;
  background: #fff;
}

.goods_left {
  display: flex;
  align-items: center;
  flex: 1;
}

.goods_img {
  width: 48px;
  height: 48px;
  border-radius: 4px;
  margin-right: 12px;
  object-fit: cover;
}

.goods_info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.goods_extra {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #333;

  .stock-tag {
    /* background-color: #fff3cd; */
    border: 1px solid #e88b32;
    border-radius: 12rpx;
    padding: 0rpx 30rpx;
    font-size: 24rpx;
    color: #e88b32;
    white-space: nowrap;
  }
}

.goods_name {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}

.goods_code {
  font-size: 12px;
  color: #888;
  margin: 2px 0;
}

.goods_price {
  display: flex;
  align-items: center;
  margin-top: 2px;
}

.price {
  color: #e22;
  font-size: 14px;
  font-weight: bold;
  margin-right: 2px;
}

.unit {
  color: #e22;
  font-size: 12px;
}

.goods_num {
  display: flex;
  align-items: center;
  margin-left: 10px;
}

.goods_num_input {
  text-align: center;
  font-size: 15px;
  margin: 0 6px;
  color: #222;
}

.cart_mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
}

.cart_popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 1001;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
}

.cart_container {
  overflow-y: auto;
  max-height: 60vh;
  background-color: #fff;
}

.popup-btn {
  width: 100%;
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.operatingButton {
  width: 90%;
  height: 100rpx;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.selectGoods {
  width: 50%;
}

::v-deep .operatingButton .u-button.data-v-3bf2dba7 {
  height: 35px;
  width: 90%;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}
</style>
